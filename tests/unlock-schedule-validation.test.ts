import { calculateUnlockSchedule } from "../src/lib/unlock-schedule-calculator";

// This test can be run with: node -r esbuild-register tests/unlock-schedule-validation.test.ts
// Or integrated into a test framework like Jest

// Raw project data for validation
const RAW_PROJECT_DATA = {
  data: {
    vesting: {
      coin_id: 194_433,
      total_start_date: "2025-01-23T00:00:00.000Z",
      tge_start_date: "2025-01-23T00:00:00.000Z",
      links: ["https://www.anime.xyz/tokenomics"],
      is_hidden: false,
      createdAt: "2025-01-24T14:06:19.906Z",
      updatedAt: "2025-01-24T14:06:19.906Z",
    },
    allocations: [
      {
        name: "Azuki Community",
        tokens_percent: 37.5,
        tokens: 3_750_000_000,
        unlock_type: "nonlinear",
        unlock_frequency_type: null,
        unlock_frequency_value: null,
        vesting_duration_type: null,
        vesting_duration_value: null,
        round_date: null,
        batches: [
          {
            date: "2025-01-23T00:00:00.000Z",
            is_tge: true,
            unlock_percent: 100,
          },
        ],
      },
      {
        name: "Domain Expansion",
        tokens_percent: 24.44,
        tokens: 2_444_000_000,
        unlock_type: "linear",
        unlock_frequency_type: "month",
        unlock_frequency_value: 1,
        vesting_duration_type: "month",
        vesting_duration_value: 30,
        round_date: "2025-07-23T00:00:00.000Z",
        batches: [
          {
            date: "2025-01-23T00:00:00.000Z",
            is_tge: true,
            unlock_percent: 51.7,
          },
          {
            date: "2025-07-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2025-08-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2025-09-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2025-10-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2025-11-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2025-12-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2026-01-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2026-02-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2026-03-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2026-04-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2026-05-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2026-06-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2026-07-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2026-08-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2026-09-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2026-10-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2026-11-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2026-12-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2027-01-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2027-02-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2027-03-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2027-04-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2027-05-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2027-06-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2027-07-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2027-08-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2027-09-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2027-10-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2027-11-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2027-12-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
          {
            date: "2028-01-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 1.558,
          },
        ],
      },
      {
        name: "Team",
        tokens_percent: 15.62,
        tokens: 1_562_000_000,
        unlock_type: "linear",
        unlock_frequency_type: "month",
        unlock_frequency_value: 1,
        vesting_duration_type: "year",
        vesting_duration_value: 2,
        round_date: "2026-01-23T00:00:00.000Z",
        batches: [
          {
            date: "2026-01-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 33.333,
          },
          {
            date: "2026-02-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-03-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-04-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-05-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-06-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-07-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-08-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-09-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-10-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-11-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-12-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-01-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-02-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-03-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-04-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-05-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-06-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-07-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-08-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-09-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-10-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-11-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-12-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2028-01-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
        ],
      },
      {
        name: "Community Cultivation",
        tokens_percent: 13,
        tokens: 1_300_000_000,
        unlock_type: "linear",
        unlock_frequency_type: "month",
        unlock_frequency_value: 1,
        vesting_duration_type: "year",
        vesting_duration_value: 3,
        round_date: "2025-01-23T00:00:00.000Z",
        batches: [
          {
            date: "2025-01-23T00:00:00.000Z",
            is_tge: true,
            unlock_percent: 25,
          },
          {
            date: "2025-02-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2025-03-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2025-04-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2025-05-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2025-06-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2025-07-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2025-08-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2025-09-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2025-10-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2025-11-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2025-12-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2026-01-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2026-02-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2026-03-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2026-04-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2026-05-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2026-06-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2026-07-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2026-08-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2026-09-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2026-10-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2026-11-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2026-12-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2027-01-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2027-02-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2027-03-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2027-04-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2027-05-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2027-06-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2027-07-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2027-08-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2027-09-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2027-10-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2027-11-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2027-12-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
          {
            date: "2028-01-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.083,
          },
        ],
      },
      {
        name: "Company",
        tokens_percent: 7.44,
        tokens: 744_000_000,
        unlock_type: "linear",
        unlock_frequency_type: "month",
        unlock_frequency_value: 1,
        vesting_duration_type: "year",
        vesting_duration_value: 2,
        round_date: "2026-01-23T00:00:00.000Z",
        batches: [
          {
            date: "2026-01-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 33.333,
          },
          {
            date: "2026-02-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-03-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-04-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-05-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-06-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-07-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-08-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-09-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-10-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-11-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2026-12-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-01-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-02-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-03-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-04-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-05-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-06-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-07-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-08-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-09-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-10-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-11-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2027-12-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
          {
            date: "2028-01-23T00:00:00.000Z",
            is_tge: false,
            unlock_percent: 2.778,
          },
        ],
      },
      {
        name: "Partner Communities",
        tokens_percent: 2,
        tokens: 200_000_000,
        unlock_type: "nonlinear",
        unlock_frequency_type: null,
        unlock_frequency_value: null,
        vesting_duration_type: null,
        vesting_duration_value: null,
        round_date: null,
        batches: [
          {
            date: "2025-01-23T00:00:00.000Z",
            is_tge: true,
            unlock_percent: 100,
          },
        ],
      },
    ],
  },
};

// Mock project data structure that the calculator expects
type MockProject = {
  tgeStartDate: Date;
  totalTokenSupply: string;
  allocationPools: Array<{
    name: string;
    allocations: Array<{
      tokenAmount: string;
      unlockSchedules: Array<{
        unlockStartDate: Date;
        initialUnlockDecimal: number;
        lockupCliffDuration: string;
        lockupTotalDuration: string;
        unlockFrequency: string;
        unlockDecimalPerPeriod: number | null;
      }>;
      vestingSchedules?: Array<{
        vestingType: string;
        vestingMilestones: Array<{
          vestingDate: Date;
          vestingDecimal: number | null;
          vestingTokenAmount: number | null;
          isTge: boolean;
          isCliff: boolean;
        }>;
      }>;
    }>;
  }>;
};

/**
 * Calculate expected unlock percentage at a given date from raw batch data
 */
function calculateExpectedUnlock(
  allocation: (typeof RAW_PROJECT_DATA.data.allocations)[0],
  targetDate: Date
): number {
  let cumulativeUnlock = 0;

  for (const batch of allocation.batches) {
    const batchDate = new Date(batch.date);
    if (batchDate <= targetDate) {
      cumulativeUnlock += batch.unlock_percent;
    }
  }

  return Math.min(cumulativeUnlock, 100); // Cap at 100%
}

/**
 * Create mock project data structure from raw data
 * This simulates what would be created by the import process
 */
function createMockProject(): MockProject {
  const tgeDate = new Date(RAW_PROJECT_DATA.data.vesting.tge_start_date);

  return {
    tgeStartDate: tgeDate,
    totalTokenSupply: "10000000000",
    allocationPools: RAW_PROJECT_DATA.data.allocations.map((allocation) => {
      // Simulate import logic for each allocation
      const hasVaryingUnlocks =
        allocation.batches.length > 1 &&
        allocation.batches.some(
          (batch) =>
            Math.abs(
              batch.unlock_percent - allocation.batches[0]!.unlock_percent
            ) > 0.1
        );

      // Find TGE batch
      const tgeBatch = allocation.batches.find((batch) => batch.is_tge);
      const initialUnlockDecimal = tgeBatch ? tgeBatch.unlock_percent / 100 : 0;

      // Calculate cliff duration (simplified version)
      let cliffDuration = "P0D";
      if (allocation.round_date) {
        const roundDate = new Date(allocation.round_date);
        const diffYears =
          (roundDate.getTime() - tgeDate.getTime()) /
          (1000 * 60 * 60 * 24 * 365);
        if (diffYears >= 1) {
          cliffDuration = `P${Math.floor(diffYears)}Y`;
        }
      }

      // Calculate total duration based on actual batches
      let totalDuration = "P3Y"; // Default
      if (allocation.batches && allocation.batches.length > 0) {
        // Find the date range from first to last batch
        const sortedBatches = allocation.batches
          .map((batch) => new Date(batch.date))
          .sort((a, b) => a.getTime() - b.getTime());

        const firstBatchDate = sortedBatches[0]!;
        const lastBatchDate = sortedBatches[sortedBatches.length - 1]!;

        const totalDurationMs =
          lastBatchDate.getTime() - firstBatchDate.getTime();
        const totalDays = Math.ceil(totalDurationMs / (1000 * 60 * 60 * 24));

        if (totalDays >= 365) {
          totalDuration = `P${Math.ceil(totalDays / 365)}Y`;
        } else if (totalDays >= 30) {
          totalDuration = `P${Math.ceil(totalDays / 30)}M`;
        } else {
          totalDuration = `P${totalDays}D`;
        }
      } else if (
        allocation.vesting_duration_type &&
        allocation.vesting_duration_value
      ) {
        // Fallback to the vesting duration if no batches
        switch (allocation.vesting_duration_type) {
          case "year": {
            totalDuration = `P${allocation.vesting_duration_value}Y`;
            break;
          }
          case "month": {
            totalDuration = `P${allocation.vesting_duration_value}M`;
            break;
          }
        }
      }

      // Calculate unlock decimal per period
      let unlockDecimalPerPeriod: number | null = null;
      if (
        !hasVaryingUnlocks &&
        allocation.unlock_type === "linear" &&
        allocation.batches.length > 1
      ) {
        const nonTgeBatches = allocation.batches.filter(
          (batch) => !batch.is_tge
        );
        if (nonTgeBatches.length > 0) {
          // Use most common percentage
          const unlockPercentages = nonTgeBatches.map(
            (batch) => batch.unlock_percent
          );
          // eslint-disable-next-line unicorn/no-array-reduce
          const mostCommon = unlockPercentages.reduce((a, b, i, arr) =>
            arr.filter((v) => v === a).length >=
            arr.filter((v) => v === b).length
              ? a
              : b
          );
          unlockDecimalPerPeriod = mostCommon / 100;
        }
      }

      return {
        name: allocation.name,
        allocations: [
          {
            tokenAmount: allocation.tokens.toString(),
            unlockSchedules: [
              {
                unlockStartDate: tgeDate,
                initialUnlockDecimal,
                lockupCliffDuration: cliffDuration,
                lockupTotalDuration: totalDuration,
                unlockFrequency: hasVaryingUnlocks
                  ? "ONE_TIME"
                  : allocation.unlock_frequency_type === "month"
                    ? "MONTHLY"
                    : "ONE_TIME",
                unlockDecimalPerPeriod: hasVaryingUnlocks
                  ? null
                  : unlockDecimalPerPeriod,
              },
            ],
            vestingSchedules: hasVaryingUnlocks
              ? [
                  {
                    vestingType: "NONLINEAR",
                    vestingMilestones: allocation.batches.map((batch) => ({
                      vestingDate: new Date(batch.date),
                      vestingDecimal: batch.unlock_percent / 100,
                      vestingTokenAmount: null,
                      isTge: batch.is_tge,
                      isCliff: false,
                    })),
                  },
                ]
              : undefined,
          },
        ],
      };
    }),
  };
}

/**
 * Test validation function
 */
function validateUnlockSchedule() {
  console.log("🧪 Starting Unlock Schedule Validation Test\n");

  const mockProject = createMockProject();
  const calculatedSchedule = calculateUnlockSchedule(mockProject);

  const testDates = [
    new Date("2025-01-23T00:00:00.000Z"), // TGE
    new Date("2025-07-23T00:00:00.000Z"), // 6 months after TGE
    new Date("2026-01-23T00:00:00.000Z"), // 1 year after TGE
    new Date("2026-07-23T00:00:00.000Z"), // 1.5 years after TGE
    new Date("2027-01-23T00:00:00.000Z"), // 2 years after TGE
    new Date("2028-01-23T00:00:00.000Z"), // 3 years after TGE
  ];

  const errors: string[] = [];

  for (const testDate of testDates) {
    console.log(`\n📅 Testing date: ${testDate.toISOString().split("T")[0]}`);

    // Find the calculated timeline point for this date
    const timelinePoint = calculatedSchedule.timeline.find(
      (point) =>
        Math.abs(point.date.getTime() - testDate.getTime()) <
        24 * 60 * 60 * 1000 // Within 1 day
    );

    if (!timelinePoint) {
      console.log(`⚠️  No timeline point found for ${testDate.toISOString()}`);
      continue;
    }

    // Validate each allocation
    for (const allocation of RAW_PROJECT_DATA.data.allocations) {
      const expectedUnlockPercent = calculateExpectedUnlock(
        allocation,
        testDate
      );
      const expectedTokens = (expectedUnlockPercent / 100) * allocation.tokens;

      const calculatedTokens =
        timelinePoint.cumulativeUnlocked[allocation.name] || 0;
      const calculatedPercent = (calculatedTokens / allocation.tokens) * 100;

      const tolerance = 0.1; // 0.1% tolerance for rounding errors
      const percentDiff = Math.abs(expectedUnlockPercent - calculatedPercent);

      if (percentDiff > tolerance) {
        const error = `❌ ${allocation.name} on ${testDate.toISOString().split("T")[0]}: Expected ${expectedUnlockPercent.toFixed(3)}% (${expectedTokens.toLocaleString()} tokens), Got ${calculatedPercent.toFixed(3)}% (${calculatedTokens.toLocaleString()} tokens), Diff: ${percentDiff.toFixed(3)}%`;
        console.log(error);
        errors.push(error);
      } else {
        console.log(
          `✅ ${allocation.name}: ${calculatedPercent.toFixed(3)}% (${calculatedTokens.toLocaleString()} tokens)`
        );
      }

      // Debug: show the unlock schedule parameters for failing allocations
      if (percentDiff > tolerance) {
        const pool = mockProject.allocationPools.find(
          (p) => p.name === allocation.name
        );
        if (pool?.allocations[0]?.unlockSchedules[0]) {
          const schedule = pool.allocations[0].unlockSchedules[0];
          console.log(
            `  📋 Schedule: cliff=${schedule.lockupCliffDuration}, total=${schedule.lockupTotalDuration}, freq=${schedule.unlockFrequency}, perPeriod=${schedule.unlockDecimalPerPeriod}, initial=${schedule.initialUnlockDecimal}`
          );
          console.log(
            `  📅 Has vesting milestones: ${!!pool.allocations[0]?.vestingSchedules?.length}`
          );
        }
      }
    }
  }

  // Additional test: Check for monotonic progression (no dropoffs)
  console.log(`\n🔍 Checking for chart dropoffs (monotonic progression)...`);
  const totalUnlockedProgression: Array<{date: Date, total: number}> = [];
  
  for (const point of calculatedSchedule.timeline) {
    const totalUnlocked = Object.values(point.cumulativeUnlocked).reduce((sum, val) => sum + val, 0);
    totalUnlockedProgression.push({
      date: point.date,
      total: totalUnlocked
    });
  }
  
  // Check for decreases in total unlocked tokens
  for (let i = 1; i < totalUnlockedProgression.length; i++) {
    const prev = totalUnlockedProgression[i-1]!;
    const curr = totalUnlockedProgression[i]!;
    
    if (curr.total < prev.total) {
      const decrease = prev.total - curr.total;
      const error = `📉 DROPOFF DETECTED on ${curr.date.toISOString().split('T')[0]}: Total decreased by ${decrease.toLocaleString()} tokens (${prev.total.toLocaleString()} → ${curr.total.toLocaleString()})`;
      console.log(error);
      errors.push(error);
    }
  }
  
  // Detailed timeline around Jan 2027 for debugging
  console.log(`\n🔍 Timeline around Jan 2027 (where chart shows dropoff):`);
  const jan2027 = new Date("2027-01-23T00:00:00.000Z");
  const debugPoints = totalUnlockedProgression.filter(point => {
    const daysDiff = Math.abs(point.date.getTime() - jan2027.getTime()) / (1000 * 60 * 60 * 24);
    return daysDiff <= 90; // Within 90 days of Jan 2027
  }).slice(0, 10); // First 10 points around that date
  
  for (const point of debugPoints) {
    console.log(`  ${point.date.toISOString().split('T')[0]}: ${point.total.toLocaleString()} tokens`);
  }

  console.log(`\n📊 Test Summary:`);
  console.log(`Total timeline points: ${calculatedSchedule.timeline.length}`);
  console.log(`Pool names: ${calculatedSchedule.poolNames.join(", ")}`);
  console.log(
    `Total supply: ${calculatedSchedule.totalSupply.toLocaleString()}`
  );
  console.log(`Final total unlocked: ${totalUnlockedProgression[totalUnlockedProgression.length - 1]?.total.toLocaleString() || 0}`);

  if (errors.length === 0) {
    console.log(
      "\n🎉 All tests passed! Unlock schedule calculator matches raw data."
    );
  } else {
    console.log(`\n💥 ${errors.length} validation errors found:`);
    for (const error of errors) console.log(error);
  }

  return errors.length === 0;
}

// Run the test immediately
console.log("Running unlock schedule validation...");
validateUnlockSchedule();

export { RAW_PROJECT_DATA, validateUnlockSchedule };
