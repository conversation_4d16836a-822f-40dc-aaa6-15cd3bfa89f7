{"name": "blocksight", "version": "0.1.0", "private": true, "type": "module", "prisma": {"schema": "./prisma"}, "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:push": "prisma db push", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "db:seed": "prisma db seed", "dev": "next dev --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "lint": "eslint && prisma format", "lint:fix": "eslint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit", "prepare": "husky", "postinstall": "prisma generate"}, "dependencies": {"@aws-sdk/client-s3": "^3.758.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@clerk/nextjs": "^6.19.5", "@clerk/themes": "^2.2.47", "@hookform/resolvers": "^4.1.3", "@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^6.8.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.3", "@react-email/components": "^0.0.33", "@react-email/render": "^1.0.5", "@react-hookz/web": "^25.1.0", "@stepperize/react": "^5.1.6", "@t3-oss/env-nextjs": "^0.10.1", "@tanstack/react-query": "^5.66.8", "@tanstack/react-table": "^8.21.2", "@trpc/client": "^11.0.0-rc.795", "@trpc/react-query": "^11.0.0-rc.795", "@trpc/server": "^11.0.0-rc.795", "@vercel/blob": "^1.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.487.0", "motion": "^12.6.3", "next": "^15.2.4", "next-themes": "^0.4.4", "nuqs": "^2.4.1", "prisma": "^6.8.2", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-email": "^3.0.7", "react-hook-form": "^7.54.2", "react-number-format": "^5.4.3", "react-scan": "^0.3.3", "recharts": "^2.15.2", "resend": "^4.1.2", "server-only": "^0.0.1", "sonner": "^2.0.1", "superjson": "^2.2.2", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "validator": "^13.15.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/compat": "^1.2.7", "@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.21.0", "@tailwindcss/postcss": "^4.0.7", "@types/eslint": "^8.56.12", "@types/node": "^20.17.19", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/recharts": "^1.8.29", "@types/validator": "^13.12.3", "@typescript-eslint/eslint-plugin": "^8.24.1", "@typescript-eslint/parser": "^8.24.1", "eslint": "^9.21.0", "eslint-config-next": "^15.1.7", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-tailwindcss": "^3.18.0", "eslint-plugin-unicorn": "^57.0.0", "husky": "^9.1.7", "lint-staged": "^15.4.3", "postcss": "^8.5.3", "prettier": "3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.7", "typescript": "^5.7.3", "typescript-eslint": "^8.24.1"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "prisma format", "eslint --fix"], "*.{json,css,md}": ["prettier --write"]}}