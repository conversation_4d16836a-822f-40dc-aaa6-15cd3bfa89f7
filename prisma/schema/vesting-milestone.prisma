/**
 * VestingMilestone model for non-linear vesting schedules
 * This model stores individual vesting events with specific dates and amounts/percentages.
 * Each milestone represents a specific point in time when a certain amount or percentage
 * of tokens becomes VESTED (not unlocked - that's handled separately by unlock schedules).
 * Vesting = when tokens are earned/allocated to the holder
 * Unlocking = when vested tokens can be transferred/sold (separate concept)
 * Examples:
 * - 25% vests on 2024-06-01 (6 months after TGE)
 * - 1,000,000 tokens vest on 2024-12-01 (12 months after TGE)
 * - 10% vests on 2025-01-01 (cliff end)
 */

model VestingMilestone {
  id          String  @id @default(cuid())
  name        String? // Optional name/description for this milestone (e.g., "6 Month Cliff", "Annual Vesting")
  description String? // Optional detailed description

  // Milestone timing
  vestingDate DateTime @map("vesting_date") @db.Timestamptz(6) // When this milestone vests

  // Amount specification (use either decimal OR absolute amount, not both)
  vestingDecimal     Decimal? @map("vesting_decimal") @db.Decimal(13, 10) // Decimal of total allocation that vests (0.0 to 1.0)
  vestingTokenAmount Decimal? @map("vesting_token_amount") @db.Decimal(36, 18) // Absolute token amount that vests

  // Milestone metadata
  isTge     Boolean @default(false) @map("is_tge") // Whether this is the TGE milestone
  isCliff   Boolean @default(false) @map("is_cliff") // Whether this is a cliff milestone
  sortOrder Int     @default(0) @map("sort_order") // Order for displaying milestones

  // Timestamps
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt DateTime? @updatedAt @map("updated_at") @db.Timestamptz(6)

  // Relations
  vestingSchedule   VestingSchedule @relation(fields: [vestingScheduleId], references: [id], onDelete: Cascade)
  vestingScheduleId String          @map("vesting_schedule_id")

  @@index([vestingScheduleId])
  @@index([vestingDate])
  @@index([sortOrder])
  @@map("vesting_milestones")
}
