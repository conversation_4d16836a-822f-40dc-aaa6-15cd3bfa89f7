model Project {
  id   String  @id @default(cuid()) @map("project_id")
  ucid String? @map("project_ucid")

  // Organization relationship
  organizationId String?   @map("organization_id") // if null, project is public
  name           String?
  symbol         String?
  website        String?
  description    String?
  createdAt      DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime? @updatedAt @map("updated_at") @db.Timestamptz(6)

  // External data integration fields
  coinId         Int?      @map("coin_id") // External API identifier
  tgeStartDate   DateTime? @map("tge_start_date") @db.Timestamptz(6) // Token Generation Event date
  totalStartDate DateTime? @map("total_start_date") @db.Timestamptz(6) // Project start date

  // Project metadata
  teamSize               Int?          @map("team_size")
  investorSize           Int?          @map("investor_size")
  totalTokenSupply       BigInt?       @map("total_token_supply")
  circulatingTokenSupply BigInt?       @map("circulating_supply")
  industry               IndustryType?

  // High level allocation data
  insiderAllocationPoolDecimal  Decimal? @default(0) @db.Decimal(13, 12)
  outsiderAllocationPoolDecimal Decimal? @default(0) @db.Decimal(13, 12)

  // Relations
  allocations      Allocation[]
  vestingSchedules VestingSchedule[]
  allocationPools  AllocationPool[]
  unlockSchedules  UnlockSchedule[]

  @@map("project")
}

enum IndustryType {
  AI
  RWA
  L1
  L2
  DeFi
  DePIN
  Gaming
  Marketplace
  Oracle
  Other

  @@map("industry_type")
}
