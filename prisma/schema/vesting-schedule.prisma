model VestingSchedule {
  id          String  @id @default(cuid())
  name        String?
  description String?

  // Vesting type determines whether this is linear or non-linear vesting
  vestingType VestingType @default(LINEAR) @map("vesting_type")

  // Linear vesting fields (used when vestingType = LINEAR)
  cliffDuration    String?           @map("cliff_duration") // Cliff period in ISO 8601 format (e.g., "P12M", "P1Y")
  vestingStartDate DateTime?         @map("vesting_start_date") @db.Timestamptz(6)
  vestingEndDate   DateTime?         @map("vesting_end_date") @db.Timestamptz(6)
  vestingFrequency VestingFrequency? @map("vesting_frequency")

  // TGE (Token Generation Event) configuration
  tgeDate          DateTime? @map("tge_date") @db.Timestamptz(6) // Date of TGE
  tgeUnlockDecimal Decimal?  @map("tge_unlock_decimal") @db.Decimal(13, 10) // Percentage unlocked at TGE (0.0 to 1.0)

  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt DateTime? @updatedAt @map("updated_at") @db.Timestamptz(6)

  // Relations
  allocations       Allocation[]       @relation("AllocationVestingSchedules")
  vestingMilestones VestingMilestone[] // For non-linear vesting
  Project           Project?           @relation(fields: [projectId], references: [id])
  projectId         String?

  @@index([name])
  @@index([vestingType])
  @@map("vesting_schedules")
}

enum VestingFrequency {
  DAILY
  WEEKLY
  MONTHLY
  QUARTERLY
  ANNUALLY
  ONE_TIME // All remaining tokens unlock at once after cliff

  @@map("vesting_frequency")
}

enum VestingType {
  LINEAR // Traditional linear vesting with regular intervals
  NONLINEAR // Custom vesting with arbitrary dates and amounts

  @@map("vesting_type")
}
