/*
  Warnings:

  - You are about to drop the column `tge_date` on the `unlock_schedules` table. All the data in the column will be lost.
  - You are about to drop the column `tge_unlock_decimal` on the `unlock_schedules` table. All the data in the column will be lost.
  - Added the required column `initial_unlock_decimal` to the `unlock_schedules` table without a default value. This is not possible if the table is not empty.
  - Added the required column `unlock_start_date` to the `unlock_schedules` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "unlock_schedules" DROP COLUMN "tge_date",
DROP COLUMN "tge_unlock_decimal",
ADD COLUMN     "initial_unlock_decimal" DECIMAL(13,10) NOT NULL,
ADD COLUMN     "unlock_start_date" TIMESTAMPTZ(6) NOT NULL;
