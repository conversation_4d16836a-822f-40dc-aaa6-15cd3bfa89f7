/**
 * This file is the schema to store all allocation data.
 * AllocationPools are meant to represent allocations set at the category level (Employees, Investors, ...etc)
 * Specific Allocations are stored in the Allocation table.
 * All AllocationPools will have at least 1 Allocation that is related to it.
 * For example:
 * - Employee allocation pool may be set at 30%
 * - Employee allocation pool will have 100 related allocation entries in the Allocation table for engineers, designers ...etc.
 * - Community allocation pool may be set to 20%
 * - We may not have specific details about the community allocation pool,
 * so we will just have 1 entry in the Allocation table regarding community allocations
 */

enum PoolType {
  // insiders
  Employees
  Founders
  Investors
  Advisors

  // outsiders
  Community
  Treasury

  // other
  Other

  @@map("pool_type")
}

model AllocationPool {
  id                String    @id @default(cuid())
  projectId         String    @map("project_id")
  name              String
  description       String?
  poolType          PoolType  @map("pool_type")
  allocationDecimal Decimal   @map("allocation_decimal") @db.Decimal(13, 12)
  createdAt         DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt         DateTime? @updatedAt @map("updated_at") @db.Timestamptz(6)

  // Relations
  project     Project      @relation(fields: [projectId], references: [id], onDelete: Restrict, onUpdate: Cascade)
  allocations Allocation[]

  @@index([projectId])
  @@index([poolType])
  @@map("allocation_pools")
}

model Allocation {
  allocationId       String          @id @default(cuid()) @map("allocation_id")
  projectId          String          @map("project_id")
  categoryId         String?         @map("category_id")
  stakeholderType    StakeholderType @map("stakeholder_type")
  group              GroupType
  name               String?
  tokenAmount        Decimal         @map("token_amount") @db.Decimal(36, 18)
  totalTokenDecimal  Decimal?        @map("total_token_decimal") @db.Decimal(13, 12)
  equityDecimal      Decimal?        @map("equity_decimal") @db.Decimal(13, 12)
  equityToTokenRatio Decimal?        @map("equity_to_token_ratio") @db.Decimal(20, 15)
  isGenerated        Boolean?        @default(false) @map("is_generated")
  createdAt          DateTime        @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt          DateTime?       @updatedAt @map("updated_at") @db.Timestamptz(6)

  // Relations
  project  Project         @relation(fields: [projectId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "allocations_project_id_project_project_id_fk")
  category AllocationPool? @relation(fields: [categoryId], references: [id])

  // Many-to-many relationship with vesting schedules
  vestingSchedules VestingSchedule[] @relation("AllocationVestingSchedules")

  // Many-to-many relationship with unlock schedules (for lockup periods)
  unlockSchedules UnlockSchedule[]

  @@index([projectId])
  @@index([categoryId])
  @@index([group])
  @@index([stakeholderType])
  @@map("allocations")
}
