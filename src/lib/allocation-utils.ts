/**
 * Allocation-specific utility functions for converting between decimal and percentage values
 * for allocation data after removing redundant percentage fields from the database.
 *
 * Note: General formatting functions have been moved to ~/lib/formatters.ts
 */

import { getNumericValue } from "~/lib/formatters";

/**
 * Convert a decimal value (0-1) to percentage (0-100) for allocation calculations
 * @param decimal - The decimal value (e.g., 0.155)
 * @returns The percentage value (e.g., 15.5)
 */
export function decimalToPercentage(
  decimal: number | string | null | undefined | { toString(): string }
): number {
  const numericValue = getNumericValue(decimal);
  return numericValue * 100;
}

/**
 * Convert a percentage value (0-100) to decimal (0-1) for allocation calculations
 * @param percentage - The percentage value (e.g., 15.5)
 * @returns The decimal value (e.g., 0.155)
 */
export function percentageToDecimal(
  percentage: number | string | null | undefined | { toString(): string }
): number {
  const numericValue = getNumericValue(percentage);
  return numericValue / 100;
}

/**
 * Calculate the total allocation percentage from an array of allocations
 * @param allocations - Array of allocation objects with decimal values
 * @param decimalField - The field name containing the decimal value (default: 'allocationDecimal')
 * @returns Total percentage (0-100)
 */
export function calculateTotalAllocationPercentage<
  T extends Record<string, unknown>,
>(
  allocations: T[],
  decimalField: keyof T = "allocationDecimal" as keyof T
): number {
  let total = 0;
  for (const allocation of allocations) {
    const decimal = allocation[decimalField] as
      | number
      | string
      | null
      | undefined
      | { toString(): string };
    total += decimalToPercentage(decimal);
  }
  return total;
}

/**
 * Validate that allocation percentages don't exceed 100%
 * @param allocations - Array of allocation objects with decimal values
 * @param decimalField - The field name containing the decimal value
 * @returns Object with isValid boolean and totalPercentage number
 */
export function validateAllocationPercentages<
  T extends Record<string, unknown>,
>(
  allocations: T[],
  decimalField: keyof T = "allocationDecimal" as keyof T
): { isValid: boolean; totalPercentage: number; remainingPercentage: number } {
  const totalPercentage = calculateTotalAllocationPercentage(
    allocations,
    decimalField
  );
  const remainingPercentage = Math.max(0, 100 - totalPercentage);

  return {
    isValid: totalPercentage <= 100,
    totalPercentage,
    remainingPercentage,
  };
}
