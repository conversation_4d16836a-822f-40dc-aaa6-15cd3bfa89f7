/**
 * Standardized color mappings for enum types across the application
 * These colors are designed to be consistent and accessible in both light and dark themes
 */

import type {
  IndustryType,
  StakeholderType,
  PoolType,
  GroupType,
  VestingFrequency,
  VestingType,
} from "@prisma/client";

// Industry Type Colors
export const INDUSTRY_COLORS: Record<IndustryType, string> = {
  AI: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300",
  RWA: "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300",
  L1: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
  L2: "bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-300",
  DeFi: "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300",
  DePIN: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300",
  Gaming:
    "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300",
  Marketplace:
    "bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-300",
  Oracle: "bg-cyan-100 text-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-300",
  Other: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300",
};

// Stakeholder Type Colors
export const STAKEHOLDER_TYPE_COLORS: Record<StakeholderType, string> = {
  Founder:
    "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300",
  Employee: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300",
  Investor:
    "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
  Advisor:
    "bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-300",
  Consultant:
    "bg-cyan-100 text-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-300",
  ExEmployee:
    "bg-slate-100 text-slate-800 dark:bg-slate-900/20 dark:text-slate-300",
  Other: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300",
};

// Pool Type Colors
export const POOL_TYPE_COLORS: Record<PoolType, string> = {
  Employees: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300",
  Founders:
    "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300",
  Investors:
    "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
  Advisors:
    "bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-300",
  Community: "bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-300",
  Treasury:
    "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300",
  Other: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300",
};

// Group Type Colors
export const GROUP_TYPE_COLORS: Record<GroupType, string> = {
  SeriesA:
    "bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-300",
  SeriesB: "bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-300",
  Team: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300",
  Common:
    "bg-slate-100 text-slate-800 dark:bg-slate-900/20 dark:text-slate-300",
  Founders:
    "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300",
  Advisors:
    "bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-300",
  LabsTreasury:
    "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300",
  PreTgeConsultants:
    "bg-cyan-100 text-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-300",
  OptionsAvailable:
    "bg-lime-100 text-lime-800 dark:bg-lime-900/20 dark:text-lime-300",
  BOD: "bg-violet-100 text-violet-800 dark:bg-violet-900/20 dark:text-violet-300",
  BoardMember:
    "bg-fuchsia-100 text-fuchsia-800 dark:bg-fuchsia-900/20 dark:text-fuchsia-300",
  Advisor:
    "bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-300",
  Other: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300",
};

// Vesting Frequency Colors
export const VESTING_FREQUENCY_COLORS: Record<VestingFrequency, string> = {
  DAILY: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300",
  WEEKLY:
    "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300",
  MONTHLY: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300",
  QUARTERLY:
    "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
  ANNUALLY:
    "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300",
  ONE_TIME: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300",
};

// Vesting Type Colors
export const VESTING_TYPE_COLORS: Record<VestingType, string> = {
  LINEAR: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300",
  NONLINEAR:
    "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300",
};

// Helper functions to get colors by enum value
export function getIndustryColor(industry: IndustryType | string): string {
  return INDUSTRY_COLORS[industry as IndustryType] || INDUSTRY_COLORS.Other;
}

export function getStakeholderTypeColor(
  stakeholderType: StakeholderType | string
): string {
  return (
    STAKEHOLDER_TYPE_COLORS[stakeholderType as StakeholderType] ||
    STAKEHOLDER_TYPE_COLORS.Other
  );
}

export function getPoolTypeColor(poolType: PoolType | string): string {
  return POOL_TYPE_COLORS[poolType as PoolType] || POOL_TYPE_COLORS.Other;
}

export function getGroupTypeColor(groupType: GroupType | string): string {
  return GROUP_TYPE_COLORS[groupType as GroupType] || GROUP_TYPE_COLORS.Other;
}

export function getVestingFrequencyColor(
  frequency: VestingFrequency | string
): string {
  return (
    VESTING_FREQUENCY_COLORS[frequency as VestingFrequency] ||
    VESTING_FREQUENCY_COLORS.ONE_TIME
  );
}

export function getVestingTypeColor(vestingType: VestingType | string): string {
  return (
    VESTING_TYPE_COLORS[vestingType as VestingType] ||
    VESTING_TYPE_COLORS.LINEAR
  );
}

// Status colors for general use
export const STATUS_COLORS = {
  success:
    "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
  warning:
    "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300",
  error: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300",
  info: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300",
  neutral: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300",
} as const;

// Priority colors
export const PRIORITY_COLORS = {
  high: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300",
  medium:
    "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300",
  low: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300",
} as const;
