import { auth, currentUser } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import * as React from "react";
import { AppSidebar } from "~/components/sidebar/app-sidebar";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "~/components/ui/sidebar";

export default async function AppLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const user = await currentUser();
  const { redirectToSignIn, orgId } = await auth();

  if (!user) {
    return redirectToSignIn();
  }

  if (!orgId) {
    redirect("/join");
  }

  return (
    <SidebarProvider>
      <AppSidebar superadmin={!!user.privateMetadata.superadmin} />
      <SidebarInset className="max-h-screen max-w-full overflow-auto">
        <div className="sticky top-0 flex h-14 items-center gap-4 border-b px-4 md:hidden">
          <SidebarTrigger />
        </div>
        <main className="max-w-full flex-1 overflow-auto p-4 md:p-8">
          {children}
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
