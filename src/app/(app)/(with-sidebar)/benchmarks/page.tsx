"use client";

import { ArrowLeftIcon } from "lucide-react";
import { useQueryState } from "nuqs";
import {
  Allocation,
  AllocationDetailView,
} from "~/app/(app)/(with-sidebar)/benchmarks/allocation-detail-view";
import { AllocationTable } from "~/app/(app)/(with-sidebar)/benchmarks/allocation-table";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "~/components/ui/breadcrumb";
import { Button } from "~/components/ui/button";
import { cn } from "~/lib/utils";
import { api } from "~/trpc/react";

export default function BenchmarksPage() {
  const { data: allocations, isPending } = api.allocation.getAll.useQuery();
  const [benchmarkId, setBenchmarkId] = useQueryState<string | null>(
    "benchmarkId",
    {
      defaultValue: null,
      parse: (value) => value ?? null,
      history: "push",
    }
  );

  // TODO: This is a hack to get the selected allocation and the ID should be changed to a real ID to be queried from the database
  const selectedAllocation = allocations?.find(
    (allocation) =>
      allocation.name === benchmarkId || allocation.group === benchmarkId
  );

  const handleSelectAllocation = (allocation: Allocation) => {
    setBenchmarkId(allocation.name || allocation.group);
  };

  const handleBackToTable = () => {
    setBenchmarkId(null);
  };

  return (
    <div className="flex h-full w-full flex-col gap-4">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink
              className="cursor-pointer"
              onClick={handleBackToTable}
            >
              Benchmarks
            </BreadcrumbLink>
          </BreadcrumbItem>
          {selectedAllocation && (
            <>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink>
                  {selectedAllocation.name || selectedAllocation.group}
                </BreadcrumbLink>
              </BreadcrumbItem>
            </>
          )}
        </BreadcrumbList>
      </Breadcrumb>

      <div className="max-w-full">
        <h1 className="text-3xl font-semibold tracking-tight">
          {selectedAllocation ? (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                className="size-8"
                onClick={handleBackToTable}
              >
                <ArrowLeftIcon className="h-4 w-4" />
                <span className="sr-only">Back</span>
              </Button>
              <div>
                <h2 className="text-2xl font-bold tracking-tight">
                  {selectedAllocation.name || selectedAllocation.group}
                </h2>
              </div>
            </div>
          ) : (
            "Benchmarks"
          )}
        </h1>
      </div>

      <div className="flex-1 overflow-auto">
        {/* Table View */}
        <div
          className={cn(
            "h-full transition-opacity duration-200",
            selectedAllocation
              ? "invisible h-0 opacity-0"
              : "visible opacity-100"
          )}
        >
          <AllocationTable
            data={allocations ?? []}
            isLoading={isPending}
            onSelectAllocation={handleSelectAllocation}
          />
        </div>

        {/* Detail View */}
        {selectedAllocation && (
          <AllocationDetailView allocation={selectedAllocation} />
        )}
      </div>
    </div>
  );
}
