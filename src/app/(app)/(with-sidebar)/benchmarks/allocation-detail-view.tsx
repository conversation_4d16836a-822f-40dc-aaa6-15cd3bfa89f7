import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";
import { ScatterCustomizedShape } from "recharts/types/cartesian/Scatter";
import { RouterOutputs } from "~/trpc/react";

export type Allocation = NonNullable<
  RouterOutputs["allocation"]["getAll"]
>[number];

interface AllocationDetailViewProps {
  allocation: Allocation;
}

type ScatterDataPoint = {
  token: number;
  tokenAmount: number;
  title: string;
  group: string;
};

interface RenderCustomizedShapeProps {
  cx: number;
  cy: number;
  fill: string;
  payload: ScatterDataPoint;
}

const renderCustomizedShape = (props: RenderCustomizedShapeProps) => {
  const { cx, cy, fill } = props;
  if (!cx || !cy) return <div />;

  return (
    <circle
      cx={cx}
      cy={cy}
      r={7}
      fill={fill}
      stroke="var(--primary-foreground)"
      strokeWidth={2}
      strokeOpacity={0.5}
      style={{
        transition: "all 0.2s ease-in-out",
        cursor: "pointer",
      }}
      onMouseEnter={(e) => {
        const target = e.target as SVGCircleElement;
        target.setAttribute("r", "10");
      }}
      onMouseLeave={(e) => {
        const target = e.target as SVGCircleElement;
        target.setAttribute("r", "7");
      }}
    />
  );
};

export const AllocationDetailView = ({
  allocation: selectedAllocation,
}: AllocationDetailViewProps) => {
  // Transform the data for the scatter plot
  const scatterData = selectedAllocation.allocations
    .map((alloc) => {
      const tokenDecimal = Number(alloc.totalTokenDecimal);
      const tokenAmount = Number(alloc.tokenAmount);

      // Skip invalid or zero values
      if (
        Number.isNaN(tokenDecimal) ||
        Number.isNaN(tokenAmount) ||
        tokenDecimal === 0 ||
        tokenAmount === 0
      ) {
        return null;
      }

      return {
        token: tokenDecimal * 100,
        tokenAmount: tokenAmount,
        title: alloc.name || alloc.group,
        group: alloc.group,
      };
    })
    .filter((data): data is NonNullable<typeof data> => data !== null);

  // Calculate summary statistics
  const totalAllocations = scatterData.length;
  const averageTokenPercentage =
    totalAllocations > 0
      ? scatterData.reduce((sum, data) => sum + data.token, 0) /
        totalAllocations
      : 0;
  const averageTokenAmount =
    totalAllocations > 0
      ? scatterData.reduce((sum, data) => sum + data.tokenAmount, 0) /
        totalAllocations
      : 0;
  const totalTokenAmount = scatterData.reduce(
    (sum, data) => sum + data.tokenAmount,
    0
  );

  return (
    <div className="space-y-6">
      <div className="h-[400px] w-full">
        <ResponsiveContainer width="100%" height="100%">
          <ScatterChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
            <CartesianGrid strokeDasharray="5 5" stroke="var(--muted)" />

            <XAxis
              type="number"
              dataKey="tokenAmount"
              name="Token Amount"
              axisLine={false}
              tickLine={false}
              fontSize={12}
              tickFormatter={(value) => {
                if (value === 0) return "0";
                return value.toLocaleString(undefined, {
                  maximumSignificantDigits: 3,
                });
              }}
              stroke="var(--muted-foreground)"
            />
            <YAxis
              type="number"
              dataKey="token"
              name="Token Percentage"
              axisLine={false}
              tickLine={false}
              fontSize={12}
              tickFormatter={(value) => {
                if (value === 0) return "0%";
                return `${value}%`;
              }}
              stroke="var(--muted-foreground)"
            />
            <Tooltip
              cursor={{
                strokeDasharray: "5 5",
                stroke: "var(--muted-foreground)",
              }}
              content={({ active, payload }) => {
                if (active && payload?.[0]?.payload) {
                  const data = payload[0].payload as ScatterDataPoint;
                  return (
                    <div className="bg-secondary w-[180px] space-y-1 rounded-md px-3 py-2 text-sm shadow-xl">
                      <div className="text-secondary-foreground font-medium">
                        {data.title}
                      </div>
                      <div className="text-secondary-foreground flex justify-between">
                        <span>Token %:</span>
                        <span>
                          {data.token.toLocaleString(undefined, {
                            maximumSignificantDigits: 3,
                          })}
                          %
                        </span>
                      </div>
                      <div className="text-secondary-foreground flex justify-between">
                        <span>Token Amount:</span>
                        <span>
                          {data.tokenAmount.toLocaleString(undefined, {
                            maximumSignificantDigits: 6,
                          })}
                        </span>
                      </div>
                      <div className="text-secondary-foreground flex justify-between">
                        <span>Group:</span>
                        <span>{data.group}</span>
                      </div>
                    </div>
                  );
                }
                return null;
              }}
            />
            <Scatter
              name="Benchmarks"
              data={scatterData}
              fill="var(--primary)"
              shape={renderCustomizedShape as ScatterCustomizedShape}
            />
          </ScatterChart>
        </ResponsiveContainer>
      </div>

      <div className="grid grid-cols-1 items-center justify-items-center gap-x-6 gap-y-2 rounded-md border p-4 text-sm md:grid-cols-2 lg:grid-cols-4">
        <div>
          <span className="text-muted-foreground">Total Allocations: </span>
          <span className="font-semibold">{totalAllocations}</span>
        </div>
        <div>
          <span className="text-muted-foreground">Avg Token %: </span>
          <span className="font-semibold">
            {averageTokenPercentage.toLocaleString(undefined, {
              maximumSignificantDigits: 3,
            })}
            %
          </span>
        </div>
        <div>
          <span className="text-muted-foreground">Avg Token Amount: </span>
          <span className="font-semibold">
            {averageTokenAmount.toLocaleString(undefined, {
              maximumSignificantDigits: 6,
            })}
          </span>
        </div>
        <div>
          <span className="text-muted-foreground">Total Tokens: </span>
          <span className="font-semibold">
            {totalTokenAmount.toLocaleString(undefined, {
              maximumSignificantDigits: 8,
            })}
          </span>
        </div>
      </div>
    </div>
  );
};
