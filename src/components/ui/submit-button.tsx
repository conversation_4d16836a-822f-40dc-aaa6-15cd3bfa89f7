import { type VariantProps } from "class-variance-authority";
import { LoaderCircleIcon } from "lucide-react";
import * as React from "react";
import { Button, buttonVariants } from "~/components/ui/button";
import { cn } from "~/lib/utils";

export interface SubmitButtonProps
  extends React.ComponentProps<"button">,
    VariantProps<typeof buttonVariants> {
  children: React.ReactNode;
  isSubmitting?: boolean;
  disabled?: boolean;
  asChild?: boolean;
}

export function SubmitButton({
  children,
  isSubmitting,
  disabled,
  className,
  variant,
  size,
  asChild = false,
  ...props
}: SubmitButtonProps) {
  return (
    <Button
      type="submit"
      disabled={isSubmitting || disabled}
      className={cn(
        className,
        "grid",
        "[&>*]:col-start-1 [&>*]:row-start-1 [&>*]:grid [&>*]:place-items-center"
      )}
      variant={variant}
      size={size}
      asChild={asChild}
      {...props}
    >
      <span className={cn("transition-opacity", isSubmitting && "opacity-0")}>
        {children}
      </span>
      {isSubmitting && (
        <span className="flex items-center justify-center">
          <LoaderCircleIcon className="h-4 w-4 animate-spin" />
        </span>
      )}
    </Button>
  );
}
