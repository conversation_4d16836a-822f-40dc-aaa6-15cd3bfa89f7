"use client";

import { useEffect, useRef, useState } from "react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "~/components/ui/tooltip";

export interface TruncatedTextProps {
  children: string;
  className?: string;
  tooltipSide?: "top" | "bottom" | "left" | "right";
  tooltipAlign?: "start" | "center" | "end";
}

/**
 * A component that only shows a tooltip when text is actually truncated.
 * Automatically detects if the text overflows its container and shows a tooltip with the full text.
 */
export function TruncatedText({
  children,
  className = "",
  tooltipSide = "top",
  tooltipAlign = "start",
}: TruncatedTextProps) {
  const textRef = useRef<HTMLSpanElement>(null);
  const [isTruncated, setIsTruncated] = useState(false);

  useEffect(() => {
    const checkTruncation = () => {
      if (textRef.current) {
        setIsTruncated(
          textRef.current.scrollWidth > textRef.current.clientWidth
        );
      }
    };

    checkTruncation();
    window.addEventListener("resize", checkTruncation);

    return () => window.removeEventListener("resize", checkTruncation);
  }, [children]);

  const textElement = (
    <span ref={textRef} className={`truncate ${className}`}>
      {children}
    </span>
  );

  if (!isTruncated) {
    return textElement;
  }

  return (
    <Tooltip>
      <TooltipTrigger asChild>{textElement}</TooltipTrigger>
      <TooltipContent side={tooltipSide} align={tooltipAlign}>
        <p>{children}</p>
      </TooltipContent>
    </Tooltip>
  );
}
