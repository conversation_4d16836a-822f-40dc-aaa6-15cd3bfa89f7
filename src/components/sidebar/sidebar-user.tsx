"use client";

import { useAuth } from "@clerk/nextjs";
import {
  ChevronsUpDownIcon,
  LogOutIcon,
  MoonIcon,
  SettingsIcon,
  ShieldIcon,
  SunIcon,
  Users2Icon,
} from "lucide-react";

import { useTheme } from "next-themes";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { InviteModal } from "~/components/organization/invite-modal";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "~/components/ui/sidebar";
import { TooltipProvider } from "~/components/ui/tooltip";
import { TruncatedText } from "~/components/ui/truncated-text";

export function NavUser({
  superadmin,
  fullName,
  email,
  imageUrl,
}: {
  superadmin: boolean;
  fullName: string;
  email: string;
  imageUrl: string;
}) {
  const { isMobile } = useSidebar();
  const { signOut } = useAuth();
  const router = useRouter();

  const handleLogout = async () => {
    await signOut();
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 shrink-0 rounded-lg">
                <AvatarImage
                  src={imageUrl || `https://avatar.vercel.sh/${email}`}
                  alt={fullName}
                />
                <AvatarFallback className="rounded-lg">
                  {fullName ? fullName.charAt(0) : ""}
                </AvatarFallback>
              </Avatar>
              <div className="flex max-w-[140px] min-w-0 flex-1 flex-col text-left text-sm leading-tight">
                <TooltipProvider>
                  <TruncatedText className="font-semibold">
                    {fullName}
                  </TruncatedText>
                  <TruncatedText className="text-xs">{email}</TruncatedText>
                </TooltipProvider>
              </div>
              <ChevronsUpDownIcon className="ml-auto size-4 shrink-0" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[:state(radix-dropdown-menu-trigger-width)] min-w-56 rounded-lg"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuGroup>
              <InviteModal>
                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                  <Users2Icon />
                  Manage Team
                </DropdownMenuItem>
              </InviteModal>
              <ThemeToggle />
              <DropdownMenuItem onClick={() => router.push("/settings")}>
                <SettingsIcon className="size-4" />
                Settings
              </DropdownMenuItem>
              {superadmin && (
                <DropdownMenuItem onClick={() => router.push("/admin")}>
                  <ShieldIcon />
                  Admin
                </DropdownMenuItem>
              )}
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleLogout}>
              <LogOutIcon />
              Log out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}

const ThemeToggle = () => {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Only show the toggle after mounting to avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <DropdownMenuItem
      onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
    >
      <SunIcon className="h-[1.2rem] w-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90" />
      <MoonIcon className="absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0" />
      Toggle theme
    </DropdownMenuItem>
  );
};
