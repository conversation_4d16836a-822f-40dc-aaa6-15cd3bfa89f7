"use client";

import { useMemo } from "react";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> } from "recharts";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "~/components/ui/card";
import {
  CHART_COLORS,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "~/components/ui/chart";
import type { RouterOutputs } from "~/trpc/react";

type ProjectWithDetails = NonNullable<RouterOutputs["project"]["getById"]>;

interface AllocationPieChartProps {
  project: ProjectWithDetails;
}

export function AllocationPieChart({ project }: AllocationPieChartProps) {
  const chartData = useMemo(() => {
    if (!project.allocationPools || project.allocationPools.length === 0) {
      return [];
    }

    return project.allocationPools.map((pool, index) => ({
      name: pool.name,
      value: Number(pool.allocationDecimal) * 100, // Convert to percentage
      fill: CHART_COLORS[index % CHART_COLORS.length],
      poolType: pool.poolType,
      description: pool.description,
    }));
  }, [project.allocationPools]);

  const chartConfig = useMemo(() => {
    const config: Record<string, { label: string; color: string }> = {};
    for (const [index, item] of chartData.entries()) {
      config[item.name.toLowerCase().replaceAll(/\s+/g, "_")] = {
        label: item.name,
        color: CHART_COLORS[index % CHART_COLORS.length] || "var(--chart-1)",
      };
    }
    return config;
  }, [chartData]);

  const renderCustomizedLabel = ({
    cx,
    cy,
    midAngle,
    innerRadius,
    outerRadius,
    percent,
  }: {
    cx: number;
    cy: number;
    midAngle: number;
    innerRadius: number;
    outerRadius: number;
    percent: number;
  }) => {
    if (percent < 0.05) return null; // Don't show labels for slices < 5%

    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? "start" : "end"}
        dominantBaseline="central"
        className="text-xs font-medium"
      >
        {`${(percent * 100).toFixed(1)}%`}
      </text>
    );
  };

  if (chartData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Allocation Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground flex h-[400px] items-center justify-center">
            No allocation data available
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Allocation Breakdown</CardTitle>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className="aspect-square max-h-[400px] w-full"
        >
          <PieChart>
            <Pie
              data={chartData}
              dataKey="value"
              nameKey="name"
              cx="50%"
              cy="50%"
              innerRadius={0}
              outerRadius="90%"
              strokeWidth={2}
              labelLine={false}
              label={renderCustomizedLabel}
              isAnimationActive={true}
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.fill} />
              ))}
            </Pie>
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  hideLabel
                  valueFormatter={(value) => `${Number(value).toFixed(2)}%`}
                />
              }
            />
          </PieChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
