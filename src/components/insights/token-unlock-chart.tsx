"use client";

import { useMemo } from "react";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import {
  CHART_COLORS,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "~/components/ui/chart";
import { formatTokenAmount } from "~/lib/formatters";
import { calculateUnlockSchedule } from "~/lib/unlock-schedule-calculator";
import type { RouterOutputs } from "~/trpc/react";

type ProjectWithDetails = NonNullable<RouterOutputs["project"]["getById"]>;

interface TokenUnlockChartProps {
  project: ProjectWithDetails;
}

export function TokenUnlockChart({ project }: TokenUnlockChartProps) {
  const { chartData, chartConfig, areaColors } = useMemo(() => {
    if (!project.allocationPools || project.allocationPools.length === 0) {
      return { chartData: [], chartConfig: {}, areaColors: {} };
    }

    // Calculate real unlock schedule from project data
    const unlockSchedule = calculateUnlockSchedule(project);

    if (unlockSchedule.timeline.length === 0) {
      return { chartData: [], chartConfig: {}, areaColors: {} };
    }

    // Build area colors and config from calculated pool names
    const areaColors: Record<string, string> = {};
    const chartConfig: Record<string, { label: string; color: string }> = {};

    for (const [index, poolName] of unlockSchedule.poolNames.entries()) {
      const key = poolName.toLowerCase().replaceAll(/\s+/g, "_");
      const color =
        CHART_COLORS[index % CHART_COLORS.length] || "var(--chart-1)";

      areaColors[key] = color;
      chartConfig[key] = {
        label: poolName,
        color: color,
      };
    }

    // Transform calculated timeline into chart data format
    const chartData = unlockSchedule.timeline.map((point) => {
      const dataPoint: Record<string, unknown> = {
        date: point.dateLabel,
        fullDate: point.date.toISOString(),
      };

      // Add unlock amounts for each pool
      // For stacked area charts, we need individual pool amounts, not cumulative
      for (const poolName of unlockSchedule.poolNames) {
        const key = poolName.toLowerCase().replaceAll(/\s+/g, "_");
        dataPoint[key] = point.totalUnlocked[poolName] || 0;
      }

      return dataPoint;
    });

    return { chartData, chartConfig, areaColors };
  }, [project]);

  // Custom tick formatter to show labels every 3 months
  const customTickFormatter = useMemo(() => {
    if (chartData.length === 0) return () => "";

    // Create a set of dates that should show labels (every 3 months)
    const labelsToShow = new Set<string>();

    // Always show first and last labels
    if (chartData.length > 0) {
      labelsToShow.add(chartData[0]?.date as string);
      labelsToShow.add(chartData.at(-1)?.date as string);
    }

    // Add labels every 3 months
    const dates = chartData.map((d) => new Date(d.fullDate as string));
    let lastShownMonth = -1;
    let lastShownYear = -1;

    for (const [i, date] of dates.entries()) {
      if (!date) continue;

      const month = date.getMonth();
      const year = date.getFullYear();

      // Show label if it's been 3+ months since last shown label
      // or if it's a different year
      if (
        lastShownMonth === -1 ||
        year !== lastShownYear ||
        month - lastShownMonth >= 3 ||
        (month < lastShownMonth && year > lastShownYear) // Handle year rollover
      ) {
        labelsToShow.add(chartData[i]?.date as string);
        lastShownMonth = month;
        lastShownYear = year;
      }
    }

    return (value: string) => {
      return labelsToShow.has(value) ? value : "";
    };
  }, [chartData]);

  if (chartData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Token Unlock Schedule</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground flex h-[400px] items-center justify-center">
            No unlock schedule data available
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Token Unlock Schedule</CardTitle>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-[400px] w-full">
          <AreaChart
            data={chartData}
            margin={{ top: 24, right: 24, left: 0, bottom: 0 }}
          >
            <defs>
              {Object.entries(areaColors).map(([key, color]) => (
                <linearGradient
                  id={`color-${key}`}
                  key={key}
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="5%" stopColor={color} stopOpacity={0.8} />
                  <stop offset="95%" stopColor={color} stopOpacity={0.2} />
                </linearGradient>
              ))}
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="var(--border)" />
            <XAxis
              dataKey="date"
              tick={{ fill: "var(--muted-foreground)" }}
              tickLine={false}
              axisLine={false}
              tickFormatter={customTickFormatter}
            />
            <YAxis
              tick={{ fill: "var(--muted-foreground)" }}
              tickLine={false}
              axisLine={false}
              tickFormatter={formatTokenAmount}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  valueFormatter={(value) => formatTokenAmount(Number(value))}
                />
              }
            />
            {Object.entries(areaColors).map(([key, color]) => (
              <Area
                key={key}
                type="stepAfter"
                dataKey={key}
                stackId="1"
                stroke={color}
                fill={`url(#color-${key})`}
                name={chartConfig[key]?.label || key}
              />
            ))}
            <ChartLegend
              content={<ChartLegendContent nameKey="label" />}
              verticalAlign="top"
              align="right"
              iconType="circle"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
