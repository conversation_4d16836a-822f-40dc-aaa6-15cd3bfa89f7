import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { db } from "~/server/db";

export const projectRouter = createTRPCRouter({
  getAll: protectedProcedure.query(async () => {
    const projects = await db.project.findMany({
      orderBy: {
        createdAt: "desc",
      },
    });
    return projects;
  }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      const project = await db.project.findFirst({
        where: {
          id: input.id,
        },
        include: {
          allocationPools: {
            include: {
              allocations: {
                include: {
                  vestingSchedules: {
                    include: {
                      vestingMilestones: {
                        orderBy: {
                          sortOrder: "asc",
                        },
                      },
                    },
                  },
                  unlockSchedules: true,
                },
              },
            },
            orderBy: {
              poolType: "asc",
            },
          },
          allocations: {
            include: {
              category: true,
              vestingSchedules: {
                include: {
                  vestingMilestones: {
                    orderBy: {
                      sortOrder: "asc",
                    },
                  },
                },
              },
              unlockSchedules: true,
            },
          },
          vestingSchedules: {
            include: {
              vestingMilestones: {
                orderBy: {
                  sortOrder: "asc",
                },
              },
            },
          },
          unlockSchedules: true,
        },
      });

      if (!project) {
        throw new Error("Project not found");
      }

      console.log(
        JSON.stringify(
          project,
          (_, v) => (typeof v === "bigint" ? v.toString() : v),
          2
        )
      );
      return project;
    }),
});
