import type {
  GroupType,
  IndustryType,
  PoolType,
  StakeholderType,
  VestingFrequency,
} from "@prisma/client";
import { db } from "~/server/db";
import type { ProjectData } from "~/server/schemas/project-data";

// Simple enum mapping functions
const mapToPoolType = (allocationName: string): PoolType => {
  const name = allocationName.toLowerCase();
  if (name.includes("team") || name.includes("employee")) return "Employees";
  if (name.includes("founder")) return "Founders";
  if (name.includes("advisor")) return "Advisors";
  if (name.includes("investor")) return "Investors";
  if (name.includes("community")) return "Community";
  if (name.includes("treasury")) return "Treasury";
  return "Other";
};

const mapToStakeholderType = (allocationName: string): StakeholderType => {
  const name = allocationName.toLowerCase();
  if (name.includes("team") || name.includes("employee")) return "Employee";
  if (name.includes("founder")) return "Founder";
  if (name.includes("advisor")) return "Advisor";
  if (name.includes("investor")) return "Investor";
  if (name.includes("consultant")) return "Consultant";
  return "Other";
};

const mapToGroupType = (allocationName: string): GroupType => {
  const name = allocationName.toLowerCase();
  if (name.includes("team")) return "Team";
  if (name.includes("founder")) return "Founders";
  if (name.includes("advisor")) return "Advisors";
  if (name.includes("series a")) return "SeriesA";
  if (name.includes("series b")) return "SeriesB";
  if (name.includes("common")) return "Common";
  return "Other";
};

const mapToVestingFrequency = (
  frequencyType: string | null
): VestingFrequency => {
  if (!frequencyType) return "ONE_TIME";
  switch (frequencyType.toLowerCase()) {
    case "day": {
      return "DAILY";
    }
    case "week": {
      return "WEEKLY";
    }
    case "month": {
      return "MONTHLY";
    }
    case "year": {
      return "ANNUALLY";
    }
    default: {
      return "ONE_TIME";
    }
  }
};

// Calculate cliff duration from TGE to first actual unlock date
const calculateCliffDuration = (
  allocation: ProjectData["data"]["allocations"][0],
  projectTgeDate: string
): string => {
  if (!allocation.batches || allocation.batches.length === 0) {
    return "P0D";
  }

  const tgeDate = new Date(projectTgeDate);
  const tgeBatch = allocation.batches.find((batch) => batch.is_tge);
  const nonTgeBatches = allocation.batches
    .filter((batch) => !batch.is_tge)
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  // If there's only a TGE batch with no further unlocks, no cliff
  if (tgeBatch && nonTgeBatches.length === 0) {
    return "P0D";
  }

  // If there are unlocks after TGE, cliff is from TGE to first non-TGE unlock
  if (nonTgeBatches.length > 0) {
    const firstNonTgeBatch = nonTgeBatches[0]!;
    const firstUnlockDate = new Date(firstNonTgeBatch.date);

    const diffMs = firstUnlockDate.getTime() - tgeDate.getTime();
    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (days <= 0) return "P0D";
    if (days >= 365) return `P${Math.floor(days / 365)}Y`;
    if (days >= 30) return `P${Math.floor(days / 30)}M`;
    return `P${days}D`;
  }

  return "P0D";
};

// Note: For public project data, we only create unlock schedules since vesting schedules
// are private employee-specific information not available in public data

// Main import function
export const importPublicProjectData = async (
  projectData: ProjectData,
  organizationId: string,
  uploadedBy: string,
  fileName: string,
  projectName: string,
  projectSymbol: string,
  industryType: IndustryType
) => {
  console.log("🚀 Starting project data import transaction...");

  return await db.$transaction(
    async (tx) => {
      console.log("📊 Step 1: Creating project entry...");
      // Step 1: Create Project Entry
      const totalTokenSupply = projectData.data.allocations.reduce(
        (sum, allocation) => sum + allocation.tokens,
        0
      );

      const project = await tx.project.create({
        data: {
          organizationId: null,
          name: projectName, // Use provided project name
          symbol: projectSymbol, // Use provided project symbol
          totalTokenSupply: BigInt(totalTokenSupply),
          coinId: projectData.data.vesting.coin_id,
          tgeStartDate: new Date(projectData.data.vesting.tge_start_date),
          totalStartDate: new Date(projectData.data.vesting.total_start_date),
          industry: industryType, // Set the industry type if provided
          // Skip insider/outsider calculations as requested - set to null when uncertain
          insiderAllocationPoolDecimal: null,
          outsiderAllocationPoolDecimal: null,
        },
      });

      console.log(
        `Created project: "${projectName}" (${projectSymbol}) - ID: ${project.id} for coin ${projectData.data.vesting.coin_id}`
      );

      // Step 2: Create AllocationPool entries using createManyAndReturn for better performance
      const allocationPoolsData = projectData.data.allocations.map(
        (allocation) => {
          const poolType = mapToPoolType(allocation.name);
          const allocationDecimal = allocation.tokens_percent / 100;

          return {
            projectId: project.id,
            name: allocation.name,
            description: `${allocation.name} allocation pool`,
            poolType,
            allocationDecimal,
          };
        }
      );

      const allocationPools = await tx.allocationPool.createManyAndReturn({
        data: allocationPoolsData,
      });

      console.log(`Created ${allocationPools.length} allocation pools`);

      // Step 3: Create Allocation entries
      const allocations = await Promise.all(
        projectData.data.allocations.map(async (allocation, index) => {
          const stakeholderType = mapToStakeholderType(allocation.name);
          const groupType = mapToGroupType(allocation.name);
          const tokenDecimal = allocation.tokens_percent / 100;

          return await tx.allocation.create({
            data: {
              projectId: project.id,
              categoryId: allocationPools[index]!.id,
              stakeholderType,
              group: groupType,
              name: allocation.name,
              tokenAmount: allocation.tokens,
              totalTokenDecimal: tokenDecimal,
              isGenerated: true,
            },
          });
        })
      );

      console.log(`Created ${allocations.length} allocations`);

      // Step 4: Create UnlockSchedules (public data contains unlock info, not vesting info)
      console.log("Creating unlock schedules from public allocation data...");
      const unlockSchedules = [];

      for (
        let index = 0;
        index < projectData.data.allocations.length;
        index++
      ) {
        const allocation = projectData.data.allocations[index]!;

        // Only create unlock schedules for allocations that have unlock data
        if (allocation.batches.length > 0) {
          const tgeBatch = allocation.batches.find((batch) => batch.is_tge);
          const unlockFrequency = mapToVestingFrequency(
            allocation.unlock_frequency_type
          );

          // Calculate lockup durations from the allocation data
          const cliffDuration = calculateCliffDuration(
            allocation,
            projectData.data.vesting.tge_start_date
          );
          
          // Calculate total duration from the batches data for more accuracy
          let totalDuration = "P0D";
          
          if (allocation.batches && allocation.batches.length > 0) {
            // Find the date range from first to last batch
            const sortedBatches = allocation.batches
              .map(batch => new Date(batch.date))
              .sort((a, b) => a.getTime() - b.getTime());
            
            const firstBatchDate = sortedBatches[0]!;
            const lastBatchDate = sortedBatches.at(-1)!;
            
            const totalDurationMs = lastBatchDate.getTime() - firstBatchDate.getTime();
            const totalDays = Math.ceil(totalDurationMs / (1000 * 60 * 60 * 24));
            
            if (totalDays >= 365) {
              totalDuration = `P${Math.ceil(totalDays / 365)}Y`;
            } else if (totalDays >= 30) {
              totalDuration = `P${Math.ceil(totalDays / 30)}M`;
            } else {
              totalDuration = `P${totalDays}D`;
            }
          } else if (
            allocation.vesting_duration_type &&
            allocation.vesting_duration_value
          ) {
            // Fallback to the vesting duration if no batches
            switch (allocation.vesting_duration_type) {
              case "day": {
                totalDuration = `P${allocation.vesting_duration_value}D`;
                break;
              }
              case "week": {
                totalDuration = `P${allocation.vesting_duration_value * 7}D`;
                break;
              }
              case "month": {
                totalDuration = `P${allocation.vesting_duration_value}M`;
                break;
              }
              case "year": {
                totalDuration = `P${allocation.vesting_duration_value}Y`;
                break;
              }
            }
          }

          // Always use project TGE date as the unlock start date
          const effectiveStartDate = new Date(
            projectData.data.vesting.tge_start_date
          );
          let tgeUnlockDecimal = 0;

          // Get the TGE unlock amount if there's a TGE batch
          if (tgeBatch) {
            tgeUnlockDecimal = tgeBatch.unlock_percent / 100;
          }

          // Check if we need nonlinear vesting milestones
          const sortedBatches = allocation.batches
            .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
          
          const hasVaryingUnlocks = sortedBatches.length > 1 && 
            sortedBatches.some(batch => 
              Math.abs(batch.unlock_percent - sortedBatches[0]!.unlock_percent) > 0.1
            );

          const unlockSchedule = await tx.unlockSchedule.create({
            data: {
              projectId: project.id,
              name: `${allocation.name} Unlock Schedule`,
              description: `Public unlock schedule for ${allocation.name} allocation`,
              lockupCliffDuration: cliffDuration,
              lockupTotalDuration: totalDuration,
              unlockStartDate: effectiveStartDate,
              initialUnlockDecimal: tgeUnlockDecimal,
              unlockFrequency: hasVaryingUnlocks ? "ONE_TIME" : unlockFrequency,
              unlockDecimalPerPeriod: hasVaryingUnlocks ? null :
                allocation.unlock_type === "linear" && allocation.batches.length > 1
                  ? (() => {
                      // Calculate from actual batch data for accuracy
                      const nonTgeBatches = allocation.batches.filter(batch => !batch.is_tge);
                      
                      if (nonTgeBatches.length === 0) return null;
                      
                      // Use the most common unlock percentage (excluding potential cliff unlocks)
                      const unlockPercentages = nonTgeBatches.map(batch => batch.unlock_percent);
                      const mostCommonPercent = unlockPercentages
                        .sort((a, b) => 
                          unlockPercentages.filter(p => p === a).length - 
                          unlockPercentages.filter(p => p === b).length
                        )
                        .pop();
                      
                      return mostCommonPercent ? mostCommonPercent / 100 : null;
                    })()
                  : null,
            },
          });

          // Create vesting milestones if we have varying unlocks
          if (hasVaryingUnlocks) {
            const vestingSchedule = await tx.vestingSchedule.create({
              data: {
                projectId: project.id,
                name: `${allocation.name} Vesting Schedule`,
                description: `Nonlinear vesting schedule for ${allocation.name}`,
                vestingType: "NONLINEAR",
              },
            });

            // Create milestones for each batch
            for (const batch of sortedBatches) {
              await tx.vestingMilestone.create({
                data: {
                  vestingScheduleId: vestingSchedule.id,
                  vestingDate: new Date(batch.date),
                  vestingDecimal: batch.unlock_percent / 100,
                  vestingTokenAmount: null,
                  isTge: batch.is_tge,
                  isCliff: false,
                },
              });
            }

            // Link allocation to vesting schedule
            await tx.allocation.update({
              where: { allocationId: allocations[index]!.allocationId },
              data: {
                vestingSchedules: {
                  connect: { id: vestingSchedule.id },
                },
              },
            });
          }

          // Link allocation to unlock schedule
          await tx.allocation.update({
            where: { allocationId: allocations[index]!.allocationId },
            data: {
              unlockSchedules: {
                connect: { id: unlockSchedule.id },
              },
            },
          });

          unlockSchedules.push(unlockSchedule);
          console.log(
            `Created unlock schedule for ${allocation.name} with ${allocation.batches.length} unlock events`
          );
        }
      }

      console.log(`Created ${unlockSchedules.length} unlock schedules`);

      return {
        project,
        allocationPools,
        allocations,
        unlockSchedules,
        summary: {
          projectId: project.id,
          coinId: projectData.data.vesting.coin_id,
          totalAllocations: allocations.length,
          totalTokenSupply,
          fileName,
          uploadedBy,
        },
      };
    },
    {
      timeout: 60_000, // 60 seconds timeout
    }
  );
};
